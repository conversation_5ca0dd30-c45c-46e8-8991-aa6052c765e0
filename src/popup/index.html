<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gemini Translator</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cabin:ital,wght@0,400..700;1,400..700&family=Comfortaa:wght@300..700&family=Crimson+Pro:ital,wght@0,200..900;1,200..900&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Fraunces:ital,opsz,wght@0,9..144,100..900;1,9..144,100..900&family=Geologica:wght@100..900&family=Lexend+Deca:wght@100..900&family=Literata:ital,opsz,wght@0,7..72,200..900;1,7..72,200..900&family=Mali:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Manrope:wght@200..800&family=Maven+Pro:wght@400..900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Petrona:ital,wght@0,100..900;1,100..900&family=Questrial&family=Quicksand:wght@300..700&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Space+Grotesk:wght@300..700&family=Varela+Round&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet">
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>Gemini Translator</h1>
      <button class="paste-translate-button" id="pasteTranslateButton" title="Paste & Translate">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
          <path d="M12 11l3 3-3 3"></path>
          <path d="M8 14h7"></path>
        </svg>
      </button>
      <button class="history-button" id="historyButton" title="Translation History">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 8v4l3 3"></path>
          <circle cx="12" cy="12" r="10"></circle>
        </svg>
      </button>
      <div class="history-panel" id="historyPanel">
        <h3>Translation History <button class="clear-history" id="clearHistory">Clear All</button></h3>
        <div id="historyItems">
          <div class="no-history">No translation history yet</div>
        </div>
      </div>
    </div>

    <details class="primary-settings-collapse">
      <summary><span class="summary-text">Vietnamese to English</span> <span class="keyboard-shortcut"
          id="vi-en-shortcut"><kbd>Alt</kbd><span>+</span><kbd>1</kbd></span>
      </summary>
      <div class="setting-group vi-en-section">
        <textarea id="vi-input" rows="3" placeholder="Enter Vietnamese text and press Enter..."></textarea>
        <div class="output-container">
          <div id="en-output" class="textarea-styled-output" aria-readonly="true" role="log" aria-live="polite"
            placeholder="English translation will appear here..."></div>
          <button class="copy-button" title="Copy translation">
            <img src="../../assets/icons/copy-icon.svg" width="16" height="16" alt="Copy" />
          </button>
        </div>
      </div>
    </details>

    <details class="primary-settings-collapse">
      <summary><span class="summary-text">English to Vietnamese</span> <span class="keyboard-shortcut"
          id="en-vi-shortcut"><kbd>Alt</kbd><span>+</span><kbd>2</kbd></span>
      </summary>
      <div class="setting-group translate-section">
        <textarea id="translate-input" rows="3" placeholder="Enter English text and press Enter..."></textarea>
        <div id="translate-output" class="textarea-styled-output" aria-readonly="true" role="log" aria-live="polite"
          placeholder="Vietnamese translation will appear here..."></div>
      </div>
    </details>

    <div class="settings">
      <div class="flex-row-space-between">
        <div class="setting-group">
          <label for="fontFamily">Font Style:</label>
          <select id="fontFamily">
            <option value="-apple-system, BlinkMacSystemFont, system-ui, sans-serif" selected>System Default</option>
            <option value="'Baloo 2', cursive">Baloo 2</option>
            <option value="'Be Vietnam Pro', sans-serif">Be Vietnam Pro</option>
            <option value="'Cabin', sans-serif">Cabin</option>
            <option value="'Comfortaa', cursive">Comfortaa</option>
            <option value="'Crimson Pro', serif">Crimson Pro</option>
            <option value="'Crimson Text', serif">Crimson Text</option>
            <option value="'Fira Sans', sans-serif">Fira Sans</option>
            <option value="'Fraunces', serif">Fraunces</option>
            <option value="'Geologica', sans-serif">Geologica</option>
            <option value="'Lexend Deca', sans-serif">Lexend Deca</option>
            <option value="'Literata', serif">Literata</option>
            <option value="'Mali', cursive">Mali</option>
            <option value="'Manrope', sans-serif">Manrope</option>
            <option value="'Maven Pro', sans-serif">Maven Pro</option>
            <option value="'Montserrat', sans-serif">Montserrat</option>
            <option value="'Mulish', sans-serif">Mulish</option>
            <option value="'Newsreader', serif">Newsreader</option>
            <option value="'Noto Sans', sans-serif">Noto Sans</option>
            <option value="'Nunito Sans', sans-serif">Nunito Sans</option>
            <option value="'Nunito', sans-serif">Nunito</option>
            <option value="'Open Sans', sans-serif">Open Sans</option>
            <option value="'Petrona', serif">Petrona</option>
            <option value="'Questrial', sans-serif">Questrial</option>
            <option value="'Quicksand', sans-serif">Quicksand</option>
            <option value="'Raleway', sans-serif">Raleway</option>
            <option value="'Roboto Mono', monospace">Roboto Mono</option>
            <option value="'Roboto', sans-serif">Roboto</option>
            <option value="'Space Grotesk', sans-serif">Space Grotesk</option>
            <option value="'Varela Round', sans-serif">Varela Round</option>
            <option value="'Work Sans', sans-serif">Work Sans</option>
          </select>
        </div>
        <div class="setting-group">
          <label for="fontSize">Font Size:</label>
          <div class="font-size-input">
            <button type="button" class="font-size-btn decrease" aria-label="Decrease font size">-</button>
            <input type="number" id="fontSize" min="8" max="32" value="15" step="1">
            <button type="button" class="font-size-btn increase" aria-label="Increase font size">+</button>
            <span class="unit">px</span>
          </div>
        </div>
      </div>
    </div>

    <div class="settings">
      <div class="flex-row-space-between">
        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="autoPosition" checked>
            <span class="checkbox-text">Smart positioning</span>
            <span class="tooltip-icon"
              title="When enabled, the popup will automatically position itself to fit on screen. When disabled, the popup will use the selected default position.">?</span>
          </label>
        </div>
        <div class="setting-group position-select-group">
          <label>
            Default position:
            <select id="defaultPosition">
              <option value="below">Below text</option>
              <option value="above">Above text</option>
              <option value="cursor">At cursor</option>
            </select>
          </label>
        </div>
      </div>
    </div>

    <div class="settings">
      <div class="setting-group">
        <label class="checkbox-label">
          <input type="checkbox" id="autoTranslateOnClick">
          <span class="checkbox-text">Auto-translate on popup click</span>
          <span class="tooltip-icon"
            title="When enabled, clicking anywhere on the popup will automatically detect language from clipboard and translate it instantly.">?</span>
        </label>
      </div>
    </div>

    <details class="primary-settings-collapse">
      <summary><span class="summary-text">Primary Setting</span> <span class="keyboard-shortcut"
          id="settings-shortcut"><kbd>Alt</kbd><span>+</span><kbd>S</kbd></span></summary>
      <div class="collapse-content">
        <div class="prompt-input">
          <div class="api-key-container" id="apiKeyContainer">
            <input type="password" id="apiKey" placeholder="Enter Gemini API Key (stored permanently)" />
          </div>
        </div>

        <div class="prompt-input">
          <textarea id="customPrompt" class="auto-save" placeholder="Enter custom prompt for En->Vi translation..."
            rows="3"></textarea>
        </div>
        <div class="prompt-input">
          <textarea id="customPromptViEn" class="auto-save" placeholder="Enter custom prompt for Vi->En translation..."
            rows="3"></textarea>
        </div>
      </div>
    </details>

    <div class="info">
      <p>Select text on any webpage and click the translation icon that appears.</p>
      <p class="note">Your API key is stored permanently on your device and is only sent to Google's API.</p>
    </div>

    <div class="footer">
      <p>Powered by Google Gemini 2.0 Flash</p>
    </div>
  </div>
  <script src="../lib/marked.min.js"></script>
  <script src="index.js" type="module"></script>
</body>

</html>